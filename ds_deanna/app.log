2025-05-26 08:36:22,392 - INFO - Will watch for changes in these directories: ['/root/ds_deanna']
2025-05-26 08:36:22,392 - INFO - <PERSON>vicorn running on http://0.0.0.0:9100 (Press CTRL+C to quit)
2025-05-26 08:36:22,392 - INFO - Started reloader process [11040] using StatReload
2025-05-26 08:36:23,566 - INFO - Started server process [11055]
2025-05-26 08:36:23,566 - INFO - Waiting for application startup.
2025-05-26 08:36:23,567 - INFO - Application startup complete.
2025-05-26 08:38:45,182 - INFO - Shutting down
2025-05-26 08:38:45,283 - INFO - Waiting for application shutdown.
2025-05-26 08:38:45,284 - INFO - Application shutdown complete.
2025-05-26 08:38:45,284 - INFO - Finished server process [11055]
2025-05-26 08:38:45,290 - INFO - Stopping reloader process [11040]
2025-05-26 08:40:21,559 - INFO - Will watch for changes in these directories: ['/root/ds_deanna']
2025-05-26 08:40:21,559 - INFO - Uvicorn running on http://0.0.0.0:3000 (Press CTRL+C to quit)
2025-05-26 08:40:21,559 - INFO - Started reloader process [11184] using StatReload
2025-05-26 08:40:22,674 - INFO - Started server process [11199]
2025-05-26 08:40:22,674 - INFO - Waiting for application startup.
2025-05-26 08:40:22,675 - INFO - Application startup complete.
2025-05-26 08:40:30,662 - INFO - 102.89.82.186:52429 - "GET /docs HTTP/1.1" 200
2025-05-26 08:40:31,537 - INFO - 102.89.82.186:52429 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 08:41:52,527 - INFO - 102.89.82.186:20833 - "GET /docs HTTP/1.1" 200
2025-05-26 08:41:53,155 - INFO - 102.89.82.186:20833 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 08:41:58,137 - INFO - 102.89.82.186:20833 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-26 08:42:17,292 - INFO - 102.89.82.186:22012 - "GET /reports HTTP/1.1" 200
2025-05-26 08:43:16,093 - WARNING - Invalid HTTP request received.
2025-05-26 08:43:17,306 - WARNING - Invalid HTTP request received.
2025-05-26 08:43:17,946 - WARNING - Invalid HTTP request received.
2025-05-26 08:43:18,466 - WARNING - Invalid HTTP request received.
2025-05-26 08:43:18,935 - WARNING - Invalid HTTP request received.
2025-05-26 08:43:19,503 - WARNING - Invalid HTTP request received.
2025-05-26 08:43:20,046 - WARNING - Invalid HTTP request received.
2025-05-26 11:04:17,432 - INFO - Will watch for changes in these directories: ['/root/ds_deanna']
2025-05-26 11:04:17,433 - INFO - Uvicorn running on http://0.0.0.0:3000 (Press CTRL+C to quit)
2025-05-26 11:04:17,433 - INFO - Started reloader process [13967] using StatReload
2025-05-26 11:04:18,552 - INFO - Started server process [13986]
2025-05-26 11:04:18,552 - INFO - Waiting for application startup.
2025-05-26 11:04:18,552 - INFO - Application startup complete.
2025-05-26 11:04:26,966 - INFO - 102.89.82.186:27610 - "GET /docs HTTP/1.1" 200
2025-05-26 11:04:27,651 - INFO - 102.89.82.186:27610 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 11:07:36,648 - INFO - Shutting down
2025-05-26 11:07:36,750 - INFO - Waiting for application shutdown.
2025-05-26 11:07:36,751 - INFO - Application shutdown complete.
2025-05-26 11:07:36,751 - INFO - Finished server process [13986]
2025-05-26 11:07:36,758 - INFO - Stopping reloader process [13967]
2025-05-26 11:07:39,931 - INFO - Will watch for changes in these directories: ['/root/ds_deanna']
2025-05-26 11:07:39,931 - INFO - Uvicorn running on http://0.0.0.0:3000 (Press CTRL+C to quit)
2025-05-26 11:07:39,931 - INFO - Started reloader process [14056] using StatReload
2025-05-26 11:07:41,051 - INFO - Started server process [14063]
2025-05-26 11:07:41,051 - INFO - Waiting for application startup.
2025-05-26 11:07:41,052 - INFO - Application startup complete.
2025-05-26 11:08:11,902 - INFO - 102.89.82.186:33210 - "GET /docs HTTP/1.1" 200
2025-05-26 11:08:12,525 - INFO - 102.89.82.186:33210 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 11:09:06,185 - INFO - 102.89.82.186:32588 - "GET /docs HTTP/1.1" 200
2025-05-26 11:09:06,858 - INFO - 102.89.82.186:32588 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 11:27:31,169 - INFO - 102.89.82.186:58646 - "GET /docs HTTP/1.1" 200
2025-05-26 11:27:32,240 - INFO - 102.89.82.186:58646 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 12:59:33,578 - INFO - 64.62.156.21:9455 - "GET / HTTP/1.1" 404
2025-05-26 12:59:50,635 - INFO - 64.62.156.20:8963 - "GET /favicon.ico HTTP/1.1" 404
2025-05-26 14:21:40,260 - INFO - 102.89.82.186:55537 - "GET /docs HTTP/1.1" 200
2025-05-26 14:21:40,866 - INFO - 102.89.82.186:55537 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 15:17:30,276 - WARNING - Invalid HTTP request received.
2025-05-26 15:17:34,625 - INFO - 206.168.34.72:60556 - "GET / HTTP/1.1" 404
2025-05-26 15:17:42,981 - INFO - 206.168.34.72:40812 - "GET / HTTP/1.1" 404
2025-05-26 15:17:43,265 - INFO - 206.168.34.72:40814 - "PRI %2A HTTP/2.0" 404
2025-05-26 15:17:43,266 - WARNING - Invalid HTTP request received.
2025-05-26 15:17:46,409 - INFO - 206.168.34.72:40830 - "GET /favicon.ico HTTP/1.1" 404
2025-05-26 15:17:46,520 - WARNING - Invalid HTTP request received.
2025-05-26 15:36:18,619 - INFO - 102.89.82.186:22637 - "GET /docs HTTP/1.1" 200
2025-05-26 15:36:19,437 - INFO - 102.89.82.186:22637 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 15:38:28,115 - INFO - 102.89.82.186:30725 - "GET /docs HTTP/1.1" 200
2025-05-26 15:38:28,802 - INFO - 102.89.82.186:30725 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 15:49:49,586 - INFO - 102.89.82.186:28233 - "GET /docs HTTP/1.1" 200
2025-05-26 15:49:50,271 - INFO - 102.89.82.186:28233 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 19:09:32,496 - INFO - 205.210.31.167:62872 - "GET / HTTP/1.1" 404
2025-05-26 19:12:12,916 - INFO - 147.185.132.33:61876 - "GET / HTTP/1.1" 404
2025-05-26 19:42:46,656 - INFO - 102.89.82.186:33209 - "GET /docs HTTP/1.1" 200
2025-05-26 19:42:47,513 - INFO - 102.89.82.186:33209 - "GET /openapi.json HTTP/1.1" 200
2025-05-26 19:54:40,388 - INFO - 199.45.154.113:50488 - "GET / HTTP/1.1" 404
2025-05-26 19:54:50,436 - INFO - 199.45.154.113:44194 - "GET / HTTP/1.1" 404
2025-05-26 19:54:51,053 - INFO - 199.45.154.113:44196 - "PRI %2A HTTP/2.0" 404
2025-05-26 19:54:51,054 - WARNING - Invalid HTTP request received.
2025-05-26 19:54:55,078 - INFO - 199.45.154.113:44220 - "GET /favicon.ico HTTP/1.1" 404
2025-05-26 19:54:55,516 - WARNING - Invalid HTTP request received.
2025-05-26 19:55:56,683 - INFO - 154.120.99.87:52454 - "POST /upload HTTP/1.1" 200
2025-05-26 20:09:52,864 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-26 20:09:52,877 - INFO - 154.120.99.87:52830 - "POST /generate-cheatsheet HTTP/1.1" 500
2025-05-26 20:38:09,399 - INFO - 167.94.138.165:36274 - "GET / HTTP/1.1" 404
2025-05-26 20:38:21,946 - INFO - 167.94.138.165:59684 - "GET / HTTP/1.1" 404
2025-05-26 20:38:24,745 - INFO - 167.94.138.165:44826 - "PRI %2A HTTP/2.0" 404
2025-05-26 20:38:24,745 - WARNING - Invalid HTTP request received.
2025-05-26 20:38:32,253 - INFO - 167.94.138.165:44854 - "GET /favicon.ico HTTP/1.1" 404
2025-05-26 20:38:34,118 - WARNING - Invalid HTTP request received.
2025-05-26 22:16:52,366 - WARNING - Invalid HTTP request received.
2025-05-26 22:16:56,112 - INFO - 91.196.152.212:60187 - "GET / HTTP/1.1" 404
2025-05-26 22:28:16,230 - INFO - 91.196.152.100:52195 - "GET /favicon.ico HTTP/1.1" 404
2025-05-26 23:00:41,560 - WARNING - Invalid HTTP request received.
2025-05-26 23:00:45,372 - INFO - 206.168.34.32:39258 - "GET / HTTP/1.1" 404
2025-05-26 23:00:54,669 - INFO - 206.168.34.32:59080 - "GET / HTTP/1.1" 404
2025-05-26 23:00:55,171 - INFO - 206.168.34.32:59100 - "PRI %2A HTTP/2.0" 404
2025-05-26 23:00:55,172 - WARNING - Invalid HTTP request received.
2025-05-26 23:01:01,649 - INFO - 206.168.34.32:56346 - "GET /favicon.ico HTTP/1.1" 404
2025-05-26 23:01:01,829 - WARNING - Invalid HTTP request received.
2025-05-26 23:11:33,502 - INFO - 198.235.24.9:56283 - "GET / HTTP/1.0" 404
2025-05-26 23:40:39,270 - WARNING - Invalid HTTP request received.
2025-05-26 23:40:39,596 - WARNING - Invalid HTTP request received.
2025-05-27 00:04:45,633 - INFO - 193.181.177.11:43396 - "GET / HTTP/1.1" 404
2025-05-27 00:11:49,058 - INFO - 79.124.58.198:38024 - "GET / HTTP/1.1" 404
2025-05-27 01:15:57,577 - WARNING - Invalid HTTP request received.
2025-05-27 01:16:01,273 - INFO - 162.142.125.206:42202 - "GET / HTTP/1.1" 404
2025-05-27 01:16:10,581 - INFO - 162.142.125.206:36394 - "GET / HTTP/1.1" 404
2025-05-27 01:16:11,466 - INFO - 162.142.125.206:50344 - "PRI %2A HTTP/2.0" 404
2025-05-27 01:16:11,467 - WARNING - Invalid HTTP request received.
2025-05-27 01:16:18,866 - INFO - 162.142.125.206:50418 - "GET /favicon.ico HTTP/1.1" 404
2025-05-27 01:16:19,262 - WARNING - Invalid HTTP request received.
2025-05-27 02:47:42,670 - WARNING - Invalid HTTP request received.
2025-05-27 02:47:51,773 - INFO - 112.102.196.2:57308 - "HEAD / HTTP/1.1" 404
2025-05-27 02:47:52,239 - INFO - 112.102.196.2:57346 - "GET / HTTP/1.1" 404
2025-05-27 02:48:01,981 - WARNING - Invalid HTTP request received.
2025-05-27 02:48:02,354 - WARNING - Invalid HTTP request received.
2025-05-27 04:28:39,749 - WARNING - Invalid HTTP request received.
2025-05-27 04:28:43,060 - INFO - 91.230.168.199:51223 - "GET / HTTP/1.1" 404
2025-05-27 04:35:08,689 - INFO - 195.184.76.146:34773 - "GET /favicon.ico HTTP/1.1" 404
2025-05-27 05:55:39,144 - INFO - 64.62.156.103:65221 - "GET / HTTP/1.1" 404
2025-05-27 05:55:57,910 - INFO - 64.62.156.105:35235 - "GET /favicon.ico HTTP/1.1" 404
2025-05-27 06:13:38,190 - INFO - 147.185.132.81:58852 - "GET / HTTP/1.1" 404
2025-05-27 06:54:24,391 - INFO - 198.235.24.74:64826 - "GET / HTTP/1.1" 404
2025-05-27 07:13:02,841 - INFO - 159.89.110.194:37948 - "GET / HTTP/1.1" 404
2025-05-27 07:13:03,286 - INFO - 159.89.110.194:37956 - "GET /favicon.ico HTTP/1.1" 404
2025-05-27 07:26:08,369 - INFO - 71.6.232.20:59266 - "GET / HTTP/1.1" 404
2025-05-27 07:29:04,122 - INFO - 52.53.254.152:56778 - "GET / HTTP/1.1" 404
2025-05-27 07:45:18,128 - INFO - 147.185.132.93:60904 - "GET / HTTP/1.1" 404
2025-05-27 10:23:46,856 - WARNING - Invalid HTTP request received.
2025-05-27 10:23:46,902 - WARNING - Invalid HTTP request received.
2025-05-27 10:23:46,937 - WARNING - Invalid HTTP request received.
2025-05-27 11:26:28,105 - INFO - 3.149.59.26:57912 - "GET / HTTP/1.1" 404
2025-05-27 12:34:40,732 - INFO - 3.137.73.221:39328 - "GET / HTTP/1.1" 404
2025-05-27 12:34:46,007 - INFO - 3.137.73.221:39336 - "GET / HTTP/1.1" 404
2025-05-27 12:36:03,169 - WARNING - Invalid HTTP request received.
2025-05-27 12:36:54,238 - WARNING - Invalid HTTP request received.
2025-05-27 12:39:18,878 - WARNING - Invalid HTTP request received.
2025-05-27 15:52:08,501 - INFO - 37.19.205.219:25686 - "GET /docs HTTP/1.1" 200
2025-05-27 15:52:09,853 - INFO - 37.19.205.219:25686 - "GET /openapi.json HTTP/1.1" 200
2025-05-27 15:52:21,550 - INFO - 37.19.205.219:48682 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 15:52:54,362 - INFO - 37.19.205.219:50927 - "POST /upload HTTP/1.1" 200
2025-05-27 15:53:39,988 - INFO - 37.19.205.219:22254 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 15:57:21,328 - INFO - 37.19.205.219:22646 - "GET /reports HTTP/1.1" 200
2025-05-27 15:58:31,683 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 15:58:33,359 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 15:58:34,012 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 15:58:34,015 - INFO - 37.19.205.219:23441 - "POST /analysis-results?patient_s3_prefix=uploads HTTP/1.1" 200
2025-05-27 15:59:21,522 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 15:59:21,906 - INFO - 37.19.205.219:42037 - "POST /generate-report HTTP/1.1" 200
2025-05-27 15:59:54,807 - INFO - 37.19.205.219:54866 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:00:29,525 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:00:31,050 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:00:31,054 - INFO - 37.19.205.219:23540 - "POST /generate-cheatsheet HTTP/1.1" 500
2025-05-27 16:20:38,952 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:20:39,315 - INFO - 37.19.205.219:35294 - "POST /generate-report HTTP/1.1" 200
2025-05-27 16:21:16,422 - INFO - 37.19.205.219:16250 - "POST /search-similar?target_s3_key=upload HTTP/1.1" 404
2025-05-27 16:21:25,969 - INFO - 37.19.205.219:17803 - "POST /search-similar?target_s3_key=reports HTTP/1.1" 404
2025-05-27 16:21:53,487 - INFO - 37.19.205.219:2090 - "GET /s3/files?folder=reports HTTP/1.1" 200
2025-05-27 16:22:18,710 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:22:19,489 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:22:19,492 - INFO - 37.19.205.219:58801 - "POST /search-similar?target_s3_key=reports%2Fgenerated-reports%2F0fbccf89-1327-40da-881f-cd45fde6fe64.txt HTTP/1.1" 200
2025-05-27 16:26:15,562 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:26:17,172 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:26:18,993 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:26:18,995 - INFO - 37.19.205.219:40018 - "POST /generate-cheatsheet HTTP/1.1" 500
2025-05-27 16:29:51,616 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:29:51,968 - INFO - 102.89.82.45:10630 - "POST /generate-report HTTP/1.1" 200
2025-05-27 16:30:29,773 - INFO - 102.89.82.45:11257 - "POST /generate-report HTTP/1.1" 422
2025-05-27 16:31:11,630 - INFO - 102.89.82.45:19963 - "POST /generate-report HTTP/1.1" 422
2025-05-27 16:32:44,182 - INFO - 102.89.82.45:11878 - "GET /reports/reports/generated-reports/0fbccf89-1327-40da-881f-cd45fde6fe64.txt HTTP/1.1" 200
2025-05-27 16:33:04,881 - INFO - 102.89.82.45:13121 - "DELETE /reports/reports/generated-reports/0fbccf89-1327-40da-881f-cd45fde6fe64.txt HTTP/1.1" 200
2025-05-27 16:33:15,527 - INFO - 102.89.82.45:37312 - "GET /reports HTTP/1.1" 200
2025-05-27 16:33:24,113 - INFO - 102.89.82.45:38560 - "GET /reports HTTP/1.1" 200
2025-05-27 16:33:39,864 - INFO - 102.89.82.45:51559 - "DELETE /reports/reports/generated-reports/04fbf040-7b36-4aba-9b7b-5427c13a2885.txt HTTP/1.1" 200
2025-05-27 16:33:47,797 - INFO - 102.89.82.45:52177 - "GET /reports HTTP/1.1" 200
2025-05-27 16:34:15,338 - INFO - 102.89.82.45:61449 - "DELETE /reports/reports/generated-reports/0fbccf89-1327-40da-881f-cd45fde6fe64.txt HTTP/1.1" 200
2025-05-27 16:34:18,081 - INFO - 102.89.82.45:61449 - "DELETE /reports/reports/generated-reports/0fbccf89-1327-40da-881f-cd45fde6fe64.txt HTTP/1.1" 200
2025-05-27 16:34:22,350 - INFO - 102.89.82.45:61449 - "GET /reports HTTP/1.1" 200
2025-05-27 16:34:40,810 - INFO - 102.89.82.45:62693 - "DELETE /reports/reports/generated-reports/8ea989a7-cd02-4123-a838-b468565ff1e0.txt HTTP/1.1" 200
2025-05-27 16:34:47,455 - INFO - 102.89.82.45:24250 - "GET /reports HTTP/1.1" 200
2025-05-27 16:35:02,508 - INFO - 102.89.82.45:24872 - "DELETE /reports/reports/generated-reports/52fc451d-9517-450c-badb-b55bce23a50a.txt HTTP/1.1" 200
2025-05-27 16:35:06,149 - INFO - 102.89.82.45:24872 - "DELETE /reports/reports/generated-reports/52fc451d-9517-450c-badb-b55bce23a50a.txt HTTP/1.1" 200
2025-05-27 16:35:15,663 - INFO - 102.89.82.45:53424 - "GET /reports/reports/generated-reports/0fbccf89-1327-40da-881f-cd45fde6fe64.txt HTTP/1.1" 404
2025-05-27 16:35:22,633 - INFO - 102.89.82.45:54048 - "GET /reports HTTP/1.1" 200
2025-05-27 16:35:34,765 - INFO - 102.89.82.45:62690 - "GET /s3/files?folder=reports HTTP/1.1" 200
2025-05-27 16:35:39,780 - INFO - 102.89.82.45:62690 - "GET /s3/files?folder=temp HTTP/1.1" 200
2025-05-27 16:35:46,311 - INFO - 102.89.82.45:63314 - "GET /s3/files?folder=upload HTTP/1.1" 500
2025-05-27 16:35:52,652 - INFO - 102.89.82.45:3790 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:36:10,379 - INFO - 102.89.82.45:4417 - "DELETE /s3/files/uploads/patient-documents/25a33fb6-f9a7-42d2-af56-43428efea48b.txt%22 HTTP/1.1" 200
2025-05-27 16:36:22,301 - INFO - 102.89.82.45:21204 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:36:29,682 - INFO - 102.89.82.45:22386 - "DELETE /s3/files/uploads/patient-documents/25a33fb6-f9a7-42d2-af56-43428efea48b.txt HTTP/1.1" 200
2025-05-27 16:36:38,341 - INFO - 102.89.82.45:31092 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:36:51,291 - INFO - 102.89.82.45:31715 - "DELETE /s3/files/uploads/patient-documents/a24ae6cb-6af3-4179-871e-0c360da80700.pdf HTTP/1.1" 200
2025-05-27 16:36:57,990 - INFO - 102.89.82.45:53422 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:37:10,832 - INFO - 102.89.82.45:54043 - "DELETE /s3/files/uploads/patient-documents/0314daab-8f53-45a7-bb92-5bf20735d8ec.pdf HTTP/1.1" 200
2025-05-27 16:37:14,752 - INFO - 102.89.82.45:54043 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:37:24,892 - INFO - 102.89.82.45:8767 - "GET /health HTTP/1.1" 200
2025-05-27 16:37:44,533 - INFO - 102.89.82.45:9388 - "POST /analyze-document?s3_key=upload HTTP/1.1" 500
2025-05-27 16:37:54,537 - INFO - 102.89.82.45:33587 - "POST /analyze-document?s3_key=uploads HTTP/1.1" 500
2025-05-27 16:38:03,870 - INFO - 102.89.82.45:34204 - "POST /analysis-results?patient_s3_prefix=uploads HTTP/1.1" 500
2025-05-27 16:38:15,269 - INFO - 102.89.82.45:44713 - "POST /analysis-results?patient_s3_prefix=bb HTTP/1.1" 500
2025-05-27 16:38:34,851 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:38:35,021 - INFO - 102.89.82.45:45960 - "POST /generate-report HTTP/1.1" 200
2025-05-27 16:44:48,287 - INFO - 102.89.82.45:42849 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:44:54,797 - INFO - 102.89.82.45:43470 - "GET /s3/files?folder=analse HTTP/1.1" 500
2025-05-27 16:45:02,031 - INFO - 102.89.82.45:56530 - "GET /s3/files?folder=reports HTTP/1.1" 200
2025-05-27 16:45:11,658 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:45:11,850 - INFO - 102.89.82.45:58396 - "POST /generate-cheatsheet HTTP/1.1" 200
2025-05-27 16:45:40,033 - INFO - 102.89.82.45:19338 - "GET /s3/files?folder=cheetsheet HTTP/1.1" 500
2025-05-27 16:45:53,182 - INFO - 102.89.82.45:19964 - "GET /s3/files?folder=cheatsheets HTTP/1.1" 200
2025-05-27 16:46:41,838 - INFO - 102.89.82.45:59641 - "POST /upload HTTP/1.1" 200
2025-05-27 16:47:21,758 - INFO - 102.89.82.45:60265 - "POST /upload HTTP/1.1" 200
2025-05-27 16:47:44,930 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:47:47,031 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:47:47,035 - INFO - 102.89.82.45:34825 - "POST /analysis-results?patient_s3_prefix=upload HTTP/1.1" 200
2025-05-27 16:56:55,319 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:56:55,676 - INFO - 102.89.82.45:38557 - "POST /generate-report HTTP/1.1" 200
2025-05-27 16:57:19,822 - INFO - 102.89.82.45:39184 - "POST /analyze-document?s3_key=uploads HTTP/1.1" 500
2025-05-27 16:57:38,351 - INFO - 102.89.82.45:11252 - "GET /s3/files?folder=uploads HTTP/1.1" 200
2025-05-27 16:57:50,383 - INFO - 102.89.82.45:11874 - "POST /analyze-document?s3_key=ploads%2Fpatient-documents%2Fcb94b09e-16c4-4b3a-83ec-b2485ea2a355.pdf HTTP/1.1" 500
2025-05-27 16:58:11,098 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:58:13,057 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 16:58:13,060 - INFO - 102.89.82.45:32963 - "POST /analysis-results?patient_s3_prefix=upload HTTP/1.1" 200
2025-05-27 17:03:26,115 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 17:03:26,472 - INFO - 102.89.82.45:8146 - "POST /generate-report HTTP/1.1" 200
2025-05-27 17:03:41,021 - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 17:03:41,187 - INFO - 102.89.82.45:8765 - "POST /generate-report HTTP/1.1" 200
2025-05-27 17:09:29,275 - INFO - 102.89.82.45:54664 - "GET /docs HTTP/1.1" 200
2025-05-27 17:09:31,158 - INFO - 102.89.82.45:54664 - "GET /openapi.json HTTP/1.1" 200
2025-05-27 17:18:12,377 - INFO - 102.89.82.45:36697 - "GET /docs HTTP/1.1" 200
2025-05-27 17:18:13,157 - INFO - 102.89.82.45:36697 - "GET /openapi.json HTTP/1.1" 200
